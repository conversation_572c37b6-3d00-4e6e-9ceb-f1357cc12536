package bj.douanes.core.config.database;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

import java.util.List;

import org.springframework.core.env.Environment;

@Component
@RequiredArgsConstructor
public class DatabaseComponent {

    private static final String APP_DATASOURCES_TYPE_NAMES = "app.datasources.typeNames";
    private final Environment env;

    public String getPackage(String sourceType) {
        return this.env.getProperty(String.format("app.datasource.%s.packages", sourceType));
    }

    public String getEnv(String sourceType, String sourceName) {
        return this.env.getProperty(String.format("app.datasource.%s.%s", sourceType, sourceName));
    }

    public List<String> getEnvTypes() {
        var listType = this.env.getProperty(APP_DATASOURCES_TYPE_NAMES, "").trim();
        return java.util.Arrays.asList(listType.split(","));
    }

    public String getFirstType() {
        var listType = this.env.getProperty(APP_DATASOURCES_TYPE_NAMES, "").trim();
        return listType.split(",")[0];
    }

    public String getOrDefaultType(String type) {
        var listType = this.env.getProperty(APP_DATASOURCES_TYPE_NAMES, "").trim();
        return listType.contains(type) ? type : listType.split(",")[0];
    }
}
