package bj.douanes.core.config.database;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Aspect pour router automatiquement vers la bonne datasource
 * en fonction du package du service appelé
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@Order(1) // Exécuté avant les autres aspects
public class ServiceExecutionDatabaseRouterAspect {

    private final DatabaseComponent dbComponent;
    private final TransactionalHelper transactionalHelper;
    private final PackageDataSourceMapping packageMapping;

    /**
     * Intercepte toutes les méthodes des services dans tous les modules
     * (bj.douanes.*.service) et route automatiquement vers la bonne datasource
     * en fonction du mapping configuré dans les propriétés
     */
    @Around("execution(* bj.douanes.*.service..*.*(..))")
    public Object routeDataSource(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        // Récupérer le nom de la classe du service
        String className = proceedingJoinPoint.getTarget().getClass().getName();

        // Déterminer la datasource à utiliser
        String targetDataSource = packageMapping.getDataSourceForClass(className);
        System.out.println("===================================================");
        System.out.println("className: " + className);
        System.out.println("targetDataSource: " + targetDataSource);
        // Valider que la datasource existe
        String validatedDataSource = dbComponent.getOrDefaultType(targetDataSource.toLowerCase());
        // if (validatedDataSource == null) {
        // log.warn("Datasource '{}' non trouvée pour la classe '{}', utilisation de la
        // datasource par défaut",
        // targetDataSource, className);
        // validatedDataSource = dbComponent.first;
        // }

        log.debug("Routage automatique: {} -> datasource '{}'", className, validatedDataSource);

        try {
            // Définir le contexte de la datasource
            DatabaseContextHolder.setCtx(validatedDataSource);

            // Exécuter la méthode dans une transaction
            return this.transactionalHelper.runWithTransaction(proceedingJoinPoint::proceed);

        } finally {
            // Nettoyer le contexte
            DatabaseContextHolder.restoreCtx();
        }
    }

}
