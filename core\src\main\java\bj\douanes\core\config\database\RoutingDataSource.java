package bj.douanes.core.config.database;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

@Slf4j
public class RoutingDataSource extends AbstractRoutingDataSource {
    @Override
    protected Object determineCurrentLookupKey() {
        final String sourceType = DatabaseContextHolder.peekDataSource().orElse(null);
        if (sourceType != null) {
            log.info("Choix de la base {} pour la prochaine transaction.", sourceType);
        } else {
            log.info("Utilisation de la base par defaut pour les prochaines transactions.");
        }

        return sourceType;
    }
}
